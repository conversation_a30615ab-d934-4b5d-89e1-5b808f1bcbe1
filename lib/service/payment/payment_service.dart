import 'dart:async';
import 'dart:io';

import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/billing_client_wrappers.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';
import 'package:playlet/config/index.dart';
import 'package:playlet/i18n/trans.dart';
import 'package:playlet/model/rewards/user_balance_result.dart';
import 'package:playlet/service/payment/payment_events.dart';
import 'package:playlet/service/payment/payment_model.dart';
import 'package:playlet/service/user_service.dart';
import 'package:playlet/utils/get_extension.dart';
import 'package:playlet/utils/product_storage.dart';
import 'package:playlet/utils/payment_event_tracker.dart';
import 'package:playlet/utils/payment_debug_helper.dart';

import '../../api/store_api.dart';
import '../../common/event/event_value.dart';
import '../../common/log/ff_log.dart';
import '../../model/store/store_product_result.dart';
import '../../model/store/store_subscription_product_result.dart';
import '../../model/store/unlock_store_product_result.dart';

/// 定义支付回调函数类型
typedef PaymentCallback = void Function(PaymentCallbackType type);

class PaymentService extends GetxService {
  var currency = "".obs;
  var currencyCode = "".obs;
  var isRestore = false.obs;
  // 是否自动订阅restore
  var isAutoRestore = false.obs;
  var subProductEd = SubProductList().obs;
  var storeProductResult = StoreProductResult().obs;
  var unLockStoreProductResult = UnLockStoreProductResult().obs;
  var subProductResult = StoreSubscriptionProductResult().obs;
  final Set<String> productIds = <String>{};
  final Set<String> subProductIds = <String>{};
  final Map<String, Payment> _purchaseCompletes = {};
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  late InAppPurchase _inAppPurchase;
  Set<ProductDetails>? _products = <ProductDetails>{}; // google内购的商品对象集合
  PaymentCallback? _paymentCallback;
  Rx<UserBalanceResult> userBalance = UserBalanceResult().obs;
  // 刷新用户状态
  final UserService userService = Get.find<UserService>();

  /// 设置支付回调函数
  void setPaymentCallback(PaymentCallback? callback) =>
      _paymentCallback = callback;

  // 订阅补单场景使用字段
  var currStrScene = EventValue.topUp.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
  }

  /// 初始化 in_app_purchase 插件
  Future<void> initInAppPurchase() async {
    FFLog.error("初始化内购插件开始");
    _inAppPurchase = InAppPurchase.instance;
    try {
      //监听购买的事件
      final Stream<List<PurchaseDetails>> purchaseUpdated =
          _inAppPurchase.purchaseStream;
      _subscription = purchaseUpdated.listen(
        _listenToPurchaseUpdated,
        onDone: () {
          _subscription.cancel();
        },
        onError: (error) {
          error.printError();
          _handleError(error);
        },
      );
      await loadProducts();
      // 添加补单方法 app启动时
      startRecover(from: EventValue.launch);

      // 启动支付事件监控（仅在生产环境）
      if (Config.isProduction) {
        PaymentDebugHelper.startMonitoring(intervalSeconds: 60); // 每分钟检查一次
        FFLog.info("PaymentService initInAppPurchase -> Payment event monitoring started");
      }
    } catch (e) {
      FFLog.error("初始化内购插件失败。$e");
    }
  }

  /// 内购的购买更新监听
  void _listenToPurchaseUpdated(
      List<PurchaseDetails> purchaseDetailsList) async {
    FFLog.info("PaymentService _listenToPurchaseUpdated -> Purchase update received, count=${purchaseDetailsList.length}, isRestore=${isRestore.value}");

    for (final PurchaseDetails purchase in purchaseDetailsList) {
      FFLog.info("PaymentService _listenToPurchaseUpdated -> Processing purchase: productId=${purchase.productID}, status=${purchase.status}, isRestore=${isRestore.value}");
      _handlePurchaseStatus(purchase);

      ///检测到恢复购买 重置恢复购买倒计时
      if (purchase.status == PurchaseStatus.restored) {
        startRestoreTimer();
      }
    }
  }

  /// 处理购买状态
  void _handlePurchaseStatus(PurchaseDetails purchase) async {
    FFLog.info("PaymentService _handlePurchaseStatus -> Handling purchase status: ${purchase.status}, productId=${purchase.productID}, isRestore=${isRestore.value}");

    switch (purchase.status) {
      case PurchaseStatus.pending:
        _handlePending(purchase);
        break;
      case PurchaseStatus.canceled:
        if (_isSubscriptionProduct(purchase.productID)) {
          _handleCancelSubscription(purchase);
        } else {
          _handleCancel(purchase);
        }
        Get.dismiss();
        _completePurchaseCompleter(purchase.productID, false);
        break;
      case PurchaseStatus.error:
        _handlePurchaseError(purchase);

        var isSubscriptionProduct = _isSubscriptionProduct(purchase.productID);
        if (isSubscriptionProduct) {
          _handleErrorSubscription(purchase.error);
        } else {
          FFLog.error(purchase.error);
        }
        Get.dismiss();
        _completePurchaseCompleter(purchase.productID, false);
        break;
      case PurchaseStatus.purchased:
      case PurchaseStatus.restored: //restore
        // 检查是否是恢复购买触发的事件
        if (purchase.status == PurchaseStatus.restored || isRestore.value) {
          FFLog.info("PaymentService _handlePurchaseStatus -> Processing restored purchase: ${purchase.productID}");
        }

        if (_isSubscriptionProduct(purchase.productID)) {
          await _verifySubscription(purchase);
        } else {
          await _verifyPurchase(purchase);
        }

        break;
    }
  }

  /// 加载商品
  Future<void> loadProducts() async {
    // 线下无法初始化
    if (!await _inAppPurchase.isAvailable()) {
      FFLog.error("无法连接至商店");
      return;
    }
    // 获取服务器数据 做货币本地化
    await getProductList();
    await getSubscriptionProductList();
    await refreshUserBalance();

    var allProducts = <String>{};
    allProducts.addAll(productIds);
    allProducts.addAll(subProductIds);
    FFLog.debug("所有需要查询的商品id $allProducts");

    loadProductsBySkuId(allProducts);

    // final ProductDetailsResponse response =
    //     await _inAppPurchase.queryProductDetails(allProducts);
    //
    // if (response.notFoundIDs.isNotEmpty) {
    //   FFLog.error(
    //       "The specified product cannot be found  ${response.notFoundIDs}");
    // }
    // _products = response.productDetails;
    // if (_products!.isNotEmpty) {
    //   _paymentCallback?.call(PaymentCallbackType.fetchProductDetail);
    //   FFLog.debug("All products have been loaded. Total: ${_products!.length}");
    // }
  }

  /// 加载商品
  Future<void> loadProductsBySkuId(Set<String> skuIds) async {
    // 线下无法初始化
    if (!await _inAppPurchase.isAvailable()) {
      FFLog.error("无法连接至商店");
      return;
    }

    final ProductDetailsResponse response =
        await _inAppPurchase.queryProductDetails(skuIds);

    if (response.notFoundIDs.isNotEmpty) {
      FFLog.error(
          "The specified product cannot be found  ${response.notFoundIDs}");
    }

    for (var productDetails in response.productDetails) {
      if (productDetails.currencySymbol.isNotEmpty &&
          productDetails.currencyCode.isNotEmpty) {
        currency.value = productDetails.currencySymbol;
        currencyCode.value = productDetails.currencyCode;
        break;
      }
    }
    _products?.addAll(response.productDetails);
    if (_products!.isNotEmpty) {
      _paymentCallback?.call(PaymentCallbackType.fetchProductDetail);
      FFLog.debug("All products have been loaded. Total: ${_products!.length}");
    }
  }

  /// 启动内购流程
  ///
  /// [skuId] 商品唯一标识（SKU ID），用于识别具体的商品 服务器下发 gpSkuId or iosSkuId。
  /// [productId] 产品ID，服务器下发。
  /// [skuModelConfigId] SKU模型配置ID，服务器下发。
  /// [amount] 支付金额，主要用于日志和埋点上报,本地化货币之后的价格。
  /// [strSource] 支付来源页面/场景的字符串表示，主要用于日志和埋点上报 字符串类型。
  /// [coins] 购买获得的金币数量。
  /// [bonus] 赠送的奖励币数量。
  /// [skuType] SKU类型，可选，用于业务逻辑判断。
  /// [recover] 是否是恢复购买操作，可选。
  /// [shortPlayCode] 短剧ID，用于关联特定内容的购买。
  /// [episode] 集数，标识购买的是哪一集。
  /// [source] 支付来源的整数标识 接口需要 SourceType 中有定义。
  /// [isRetain] 是否膨胀商品。
  /// [lockBegin] 视频解锁页 解锁卡点。
  /// [resourceBitId] 资源位ID。
  /// [playDirection] 屏幕方向。
  ///
  /// 返回一个 [Future<bool>] 表示支付是否成功完成。
  Future<bool> startPurchase({
    required String skuId,
    required String productId,
    required String skuModelConfigId,
    required String amount,
    required String strSource,
    required String coins,
    required String bonus,
    String? resourceBitId,
    String? playDirection,
    int? skuType,
    bool? recover,
    int? shortPlayId,
    int? shortPlayCode,
    int? episode,
    int? source,
    bool? isRetain,
    String? lockBegin,
  }) async {
    isRestore.value = false;

    FFLog.info('''
  发起内购订单 - 
  skuId: $skuId,
  productId: $productId,
  skuModelConfigId: $skuModelConfigId,
  amount: $amount,
  coins: $coins,
  bonus: $bonus,
  source: $source,
  recover: $recover,
  strSource: $strSource,
  shortPlayCode: $shortPlayCode,
  episode: $episode,
  isRetain: $isRetain,
  skuType: $skuType,
  lockBegin: $lockBegin,
  resourceBitId: $resourceBitId
  playDirection: $playDirection
  
''');

    PaymentEvent.submitOrderClick(
        amount: amount,
        skuProductId: productId,
        skuId: skuId,
        skuModelConfigId: skuModelConfigId,
        lockBegin: lockBegin ?? "",
        strScene: strSource,
        isSubscriptionProduct: false,
        coins: coins,
        bonus: bonus,
        reelId: shortPlayCode.toString(),
        episode: episode.toString(),
        resourceBitId: resourceBitId ?? "",
        playDirection: playDirection ?? "");

    if (_products == null || _products!.isEmpty) {
      Get.toast(AppTrans.noProductsAvailable());
      FFLog.info(AppTrans.noProductsAvailable());
      return false;
    }

    final ProductDetails? productDetails = _getProduct(_products, skuId);
    if (productDetails == null) {
      FFLog.error("Product not found: $productId");
      return false;
    }
    // 创建 Completer
    Completer<bool> completer = Completer<bool>();

    var payment = Payment(
        completer: completer,
        skuId: skuId,
        skuProductId: productId,
        source: source,
        recover: recover,
        shortPlayId: shortPlayId,
        shortPlayCode: shortPlayCode,
        episode: episode,
        skuModelConfigId: skuModelConfigId,
        isRetain: isRetain,
        skuType: skuType,
        lockBegin: lockBegin,
        strSource: strSource,
        resourceBitId: resourceBitId,
        playDirection: playDirection,
        amount: amount);

    _purchaseCompletes[skuId] = payment;
    try {
      // todo 添加用户id
      var userId = Get.find<UserService>().userInfo.value?.userId;

      // obfuscatedAccountId
      // [userId,productId,接口下发的价格，场景值,0,订阅金币数,订阅奖励币数]

      var obfuscatedAccountId =
          "[$userId,$productId,0,${source ?? SourceType.rechargePage},0,$coins,$bonus]";

      FFLog.info("新增支付参数: $obfuscatedAccountId");

      var result = await _inAppPurchase.buyConsumable(
          purchaseParam: PurchaseParam(
              productDetails: productDetails,
              applicationUserName: obfuscatedAccountId));
      FFLog.debug("_inAppPurchase.buyConsumable: $result");

      PaymentEvent.submitOrderShow(
          amount: amount,
          skuProductId: productId,
          skuId: skuId,
          skuModelConfigId: skuModelConfigId,
          lockBegin: lockBegin ?? "",
          strScene: strSource,
          isSubscriptionProduct: false,
          coins: coins,
          bonus: bonus,
          reelId: shortPlayCode.toString(),
          episode: episode.toString(),
          resourceBitId: resourceBitId ?? "",
          playDirection: playDirection ?? "");
    } catch (e) {
      await _cancelAllWaitingPurchases();
      completer.complete(false);
      FFLog.error("Purchase failed: $e");
    }
    return completer.future;
  }

  /// 启动订阅流程（非消耗型商品）
  ///
  /// [skuId] 商品唯一标识（SKU ID），用于识别具体的商品 服务器下发 gpSkuId or iosSkuId。
  /// [productId] 产品ID，服务器下发。
  /// [amount] 支付金额，主要用于日志和埋点上报,本地化货币之后的价格。
  /// [strSource] 支付来源页面/场景的字符串表示，主要用于日志和埋点上报 字符串类型 例如: EventValue.adsCoins。
  /// [source] 支付来源的整数标识 接口需要 SourceType 中有定义。
  /// [recover] 是否是恢复购买操作，可选。
  /// [shortPlayCode] 短剧ID，用于关联特定内容的订阅。
  /// [episode] 集数，标识订阅的是哪一集。
  /// [skuType] SKU类型，可选，用于业务逻辑判断。
  /// [lockBegin] 视频解锁页 解锁卡点。
  /// [resourceBitId] 资源位ID。
  /// [playDirection] 屏幕方向。
  ///
  /// 返回一个 [Future<bool>] 表示订阅是否成功完成。

  Future<bool> startSubscription({
    required String skuId,
    required String productId,
    required String amount,
    required String strSource,
    String? resourceBitId,
    String? playDirection,
    int? source,
    bool? recover,
    int? shortPlayId,
    int? shortPlayCode,
    int? episode,
    int? skuType,
    String? lockBegin,
  }) async {
    isRestore.value = false;

    FFLog.info('''
  发起订阅订单 - 
  skuId: $skuId,
  productId: $productId,
  amount: $amount,
  strSource: $strSource,
  source: $source,
  recover: $recover,
  shortPlayId: $shortPlayId,
  shortPlayCode: $shortPlayCode,
  episode: $episode,
  skuType: $skuType,
  lockBegin: $lockBegin,
  resourceBitId: $resourceBitId,
  playDirection: $playDirection
''');

    // 订阅没有模版ID coins  bonus
    PaymentEvent.submitOrderClick(
        amount: amount,
        skuProductId: productId,
        skuId: skuId,
        skuModelConfigId: "",
        lockBegin: lockBegin ?? "",
        strScene: strSource,
        isSubscriptionProduct: true,
        reelId: shortPlayCode.toString(),
        episode: episode.toString(),
        // 订阅商品没有
        coins: '',
        bonus: '',
        resourceBitId: resourceBitId ?? "",
        playDirection: playDirection ?? "");

    if (_products == null || _products!.isEmpty) {
      FFLog.error("No products available for subscription");
      Get.toast(AppTrans.noProductsSubscription());
      return false;
    }
    final ProductDetails? productDetails = _getProduct(_products, skuId);
    if (productDetails == null) {
      FFLog.error("Subscription product not found: $productId");
      return false;
    }
    // 创建 Completer
    Completer<bool> completer = Completer<bool>();
    var payment = Payment(
        completer: completer,
        skuId: skuId,
        skuProductId: productId,
        source: source,
        recover: recover,
        shortPlayId: shortPlayId,
        shortPlayCode: shortPlayCode,
        episode: episode,
        skuType: skuType,
        lockBegin: lockBegin,
        strSource: strSource,
        // skuModelConfigId:skuModelConfigId
        resourceBitId: resourceBitId,
        playDirection: playDirection,
        amount: amount);

    _purchaseCompletes[skuId] = payment;
    try {
      var userId = Get.find<UserService>().userInfo.value?.userId;
      // obfuscatedAccountId
      // [userId,productId,接口下发的价格，场景值,0,订阅金币数,订阅奖励币数]
      var obfuscatedAccountId =
          "[$userId,$productId,0,${source ?? SourceType.subscriptionAggregationPage},0,0,0]";

      FFLog.info("新增支付参数: $obfuscatedAccountId");

      await _inAppPurchase.buyNonConsumable(
          purchaseParam: PurchaseParam(
              productDetails: productDetails,
              applicationUserName: obfuscatedAccountId));

      // 订阅没有模版ID coins  bonus
      PaymentEvent.submitOrderShow(
          amount: amount,
          skuProductId: productId,
          skuId: skuId,
          skuModelConfigId: "",
          lockBegin: lockBegin ?? "",
          strScene: strSource,
          isSubscriptionProduct: true,
          reelId: shortPlayCode.toString(),
          episode: episode.toString(),
          // 订阅商品没有
          coins: '',
          bonus: '',
          resourceBitId: resourceBitId ?? "",
          playDirection: playDirection ?? "");
    } catch (e) {
      FFLog.error("Subscription purchase failed: $e ${e.hashCode}");
      await _cancelAllWaitingPurchases();
      completer.complete(false);
      _paymentCallback?.call(PaymentCallbackType.subscriptionError);
    }
    return completer.future;
  }

  Timer? timer;
  bool isRestoreRunning = false;

  /// 恢复购买
  Future<void> restorePurchases({bool isAuto = false}) async {
    FFLog.info("PaymentService restorePurchases -> Restore purchases called, isAuto=$isAuto, isRestoreRunning=$isRestoreRunning");
    isRestore.value = true;
    isAutoRestore.value = isAuto;

    try {
      if (isRestoreRunning) {
        FFLog.info("PaymentService restorePurchases -> Restore process already running, skipping");
      } else {
        FFLog.info("PaymentService restorePurchases -> Starting store restore process");
        await _inAppPurchase.restorePurchases();
      }
      startRestoreTimer();
    } catch (e) {
      FFLog.error("PaymentService restorePurchases -> Restore purchases failed: $e");
    }
  }

  /// 启动或重置恢复购买定时器 10后
  void startRestoreTimer() {
    FFLog.info("重置补单定时器");
    if (timer != null && timer!.isActive) {
      timer!.cancel();
    }
    isRestoreRunning = true;
    timer = Timer(const Duration(seconds: 10), () {
      isRestoreRunning = false;
      FFLog.info("补单定时器结束 补单操作结束 是否自动补单 ${isAutoRestore.value}");
      if (!isAutoRestore.value) {
        Get.dismiss();
        Get.toast(AppTrans.reStoreNoOrder());
      }
    });
  }

  /// 根据产品ID获取产品信息
  ProductDetails? _getProduct(Set<ProductDetails>? list, String productId) {
    if (list == null) {
      return null;
    }
    for (var product in list) {
      if (product.id == productId) {
        return product;
      }
    }
    return null;
  }

  /// 判断商品是否为订阅商品
  bool _isSubscriptionProduct(String skuId) {
    return subProductIds.contains(skuId);
  }

  ///内购 购买失败处理
  void _handleError(Object? error) {
    _paymentCallback?.call(PaymentCallbackType.purchaseError);
  }

  /// 订阅失败处理
  void _handleErrorSubscription(Object? error) {
    _paymentCallback?.call(PaymentCallbackType.subscriptionError);
  }

  /// 等待支付处理
  void _handlePending(PurchaseDetails purchase) {
    FFLog.info("PaymentService _handlePending -> Waiting for payment response, productId=${purchase.productID}, isRestore=${isRestore.value}");

    final Payment? payment = _purchaseCompletes[purchase.productID];
    if (payment != null) {
      final ProductDetails? productDetails =
          _getProduct(_products, payment.skuId);

      if (productDetails != null) {
        // 只有在非恢复购买时才触发 order_create 事件
        if (!isRestore.value) {
          FFLog.info("PaymentService _handlePending -> Triggering order_create event for new purchase");
          PaymentEvent.submitOrderCreate(
            payment: payment,
            productDetails: productDetails,
            purchase: purchase,
            isSubscriptionProduct: _isSubscriptionProduct(payment.skuId),
          );
        } else {
          FFLog.info("PaymentService _handlePending -> Skipping order_create event for restore purchase");
        }
      }
    }
  }

  /// 取消支付处理
  void _handleCancel(PurchaseDetails purchase) {
    FFLog.info("支付取消");

    final Payment? payment = _purchaseCompletes[purchase.productID];
    // 上报内购订单取消
    _paymentCallback?.call(PaymentCallbackType.purchaseCancel);
    if (payment != null) {
      PaymentEvent.submitOrderCreateCancel(
        payment,
        false,
      );
    }
  }

  /// 取消订阅处理
  void _handleCancelSubscription(PurchaseDetails purchase) {
    FFLog.info("订阅支付取消");
    _paymentCallback?.call(PaymentCallbackType.subscriptionCancel);
    // 上报订阅订单取消
    final Payment? payment = _purchaseCompletes[purchase.productID];
    if (payment != null) {
      PaymentEvent.submitOrderCreateCancel(payment, true);
    }
  }

  /// 验证购买
  Future<void> _verifyPurchase(PurchaseDetails purchase) async {
    final Payment? payment = _purchaseCompletes[purchase.productID];

    if (payment != null) {
      PaymentEvent.submitStoreOrderPaySuccess(
          payment, _isSubscriptionProduct(payment.skuId), purchase);
    }

    // todo 优先保存用户支付信息
    await ProductStorage.saveUserPayment(payment, purchase);

    final bool verified =
        await _verifyPurchasePlatformSpecific(purchase, isSubscription: false);

    if (verified && purchase.pendingCompletePurchase) {
      try {
        await completeStorePurchase(purchase);
        _paymentCallback?.call(PaymentCallbackType.purchaseSuccess);

        refreshUserBalance();

        // 刷新用户信息
        if (!isRestore.value) {
          userService.fetchUserInfo();
        }

        if (payment != null) {
          PaymentEvent.submitOrderPaySuccess(
              payment, _isSubscriptionProduct(payment.skuId), purchase);
        }

        // 打印支付事件统计
        PaymentEventTracker.printStatistics();

        _completePurchaseCompleter(purchase.productID, true);
      } catch (e) {
        FFLog.error("验证内购错误 completePurchase : $e");
        _completePurchaseCompleter(purchase.productID, false);
      }
    } else {
      FFLog.error(
          "Purchase verification failed   $verified  ${purchase.pendingCompletePurchase}");
      _completePurchaseCompleter(purchase.productID, false);
    }
  }

  /// 验证订阅
  Future<void> _verifySubscription(PurchaseDetails purchase) async {
    final Payment? payment = _purchaseCompletes[purchase.productID];

    if (payment != null) {
      PaymentEvent.submitStoreOrderPaySuccess(
          payment, _isSubscriptionProduct(payment.skuId), purchase);
    }

    final bool verified =
        await _verifyPurchasePlatformSpecific(purchase, isSubscription: true);

    FFLog.debug("验证订阅  restore $verified  ${purchase.pendingCompletePurchase}");
    // if (verified && purchase.pendingCompletePurchase) {
    if (verified) {
      try {
        await completeStorePurchase(purchase);

        _paymentCallback?.call(PaymentCallbackType.subscriptionSuccess);
        // 刷新用户信息
        if (isRestore.value) {
          FFLog.debug("验证订阅  restore _verifySubscription");
          PaymentEvent.submitRestoreResult(
              type: isAutoRestore.value ? EventValue.auto : EventValue.manual,
              strScene: currStrScene.value);
          Get.dismiss();
          if (!isAutoRestore.value) {
            Get.toast(AppTrans.reStoreSuccess());
          }
        }

        getSubscriptionProductList();
        userService.fetchUserInfo();

        // 订阅支付成功埋点
        if (payment != null) {
          PaymentEvent.submitOrderPaySuccess(
              payment, _isSubscriptionProduct(payment.skuId), purchase);
        }
        _completePurchaseCompleter(purchase.productID, true);
      } catch (e) {
        _handleErrorSubscription(
            "completePurchase Error completing subscription purchase  : $e");
        _completePurchaseCompleter(purchase.productID, false);
      }
    } else {
      _handleErrorSubscription("Subscription purchase verification failed");
      _completePurchaseCompleter(purchase.productID, false);
    }
  }

  /// 根据平台验证购买
  Future<bool> _verifyPurchasePlatformSpecific(PurchaseDetails purchase,
      {required bool isSubscription}) async {
    if (Platform.isAndroid) {
      return _verifyAndroidPurchase(purchase as GooglePlayPurchaseDetails,
          isSubscription: isSubscription);
    } else if (Platform.isIOS) {
      return _verifyApplePurchase(purchase as AppStorePurchaseDetails,
          isSubscription: isSubscription);
    }

    return false;
  }

  /// Android支付验证
  Future<bool> _verifyAndroidPurchase(GooglePlayPurchaseDetails googleDetail,
      {required bool isSubscription}) async {
    if (!isAutoRestore.value) {
      Get.loading();
    }

    try {
      final originalJson = googleDetail.billingClientPurchase.originalJson;
      final signature = googleDetail.billingClientPurchase.signature;
      final String skuId = googleDetail.productID;
      final String token = googleDetail.billingClientPurchase.purchaseToken;

      if (_products == null) {
        FFLog.error("商店获取的列表为空");
        return false;
      }

      final ProductDetails? productDetails = _getProduct(_products, skuId);

      if (productDetails == null) {
        FFLog.error("根据SkuID 获取的商品为空  $skuId");
        return false;
      }

      Payment? payment = _purchaseCompletes[skuId];
      if (payment == null) {
        FFLog.error("支付信息为空  $skuId");
        if (isSubscription && isRestore.value) {
          try {
            //  这里只做订阅补单入账
            var firstMatch = subProductResult.value.productList
                ?.firstWhereOrNull((item) => item.skuId == productDetails.id);
            if (firstMatch != null) {
              payment = Payment(
                  skuId: skuId,
                  skuProductId: firstMatch.productId.toString(),
                  amount: firstMatch.firstAmount);
              FFLog.debug("补单重建Payment 参数");
            } else {
              return false;
            }
          } catch (e) {
            FFLog.debug("");
          }
        } else {
          return false;
        }
      }

      if (payment == null) {
        FFLog.error("支付信息为空  $skuId");
        return false;
      }

      final bool? result = isSubscription
          ? await ApiStore.googleSubscribePosting(
              token: token,
              skuId: payment.skuId,
              productId: payment.skuProductId,
              source: payment.source,
              recover: isRestore.value,
              shortPlayId: payment.shortPlayId,
              episode: payment.episode,
              skuType: payment.skuType,
            )
          : await ApiStore.googlePosting(
              purchaseData: originalJson,
              skuProductId: payment.skuProductId,
              skuId: payment.skuId,
              signature: signature,
              price: productDetails.rawPrice.toString(),
              shortPlayId: payment.shortPlayId,
              episode: payment.episode,
              skuModelConfigId: payment.skuModelConfigId,
              currency: currencyCode.value,
              isRetain: payment.isRetain,
              skuType: payment.skuType);

      // 正确检查结果值
      return result == true;
    } catch (e) {
      FFLog.error("验证 google 支付错误: $e");
      return false;
    } finally {
      FFLog.info(
          "订阅接口调用完毕 finally isAutoRestore.value ${isAutoRestore.value}  isRestoreRunning $isRestoreRunning  是否关闭load (${(!isAutoRestore.value || !isRestoreRunning)})");

      if (!isAutoRestore.value) {
        Get.dismiss();
      }
    }
  }

  /// Apple支付验证
  Future<bool> _verifyApplePurchase(AppStorePurchaseDetails appStoreDetail,
      {required bool isSubscription}) async {
    if (!isAutoRestore.value) {
      Get.loading();
    }

    try {
      final String receiptData =
          appStoreDetail.verificationData.serverVerificationData;
      final String skuId = appStoreDetail.productID;

      if (_products == null) {
        FFLog.error("商店获取的列表为空");
        return false;
      }
      final ProductDetails? productDetails = _getProduct(_products, skuId);

      if (productDetails == null) {
        FFLog.error("根据SkuID 获取的商品为空  $skuId");
        return false;
      }

      // todo 添加具体参数

      Payment? payment = _purchaseCompletes[skuId];
      if (payment == null) {
        FFLog.error("支付信息为空  $skuId");

        if (isSubscription && isRestore.value) {
          try {
            //  只做订阅补单入账
            var firstMatch = subProductResult.value.productList
                ?.firstWhereOrNull((item) => item.skuId == productDetails.id);
            if (firstMatch != null) {
              payment = Payment(
                  skuId: skuId,
                  skuProductId: firstMatch.productId.toString(),
                  amount: firstMatch.firstAmount);
              FFLog.debug("补单重建Payment 参数");
            } else {
              return false;
            }
          } catch (e) {
            FFLog.debug("");
          }
        } else {
          return false;
        }
      }

      if (payment == null) {
        FFLog.error("支付信息为空  $skuId");
        return false;
      }

      FFLog.error("支付参数 ${payment.toString()}");

      final bool? result = isSubscription
          ? await ApiStore.appleSubscribePosting(
              receiptData: receiptData,
              skuId: skuId,
              skuProductId: payment.skuProductId,
              payAmount: productDetails.rawPrice.toString(),
              currency: currencyCode.value,
              source: payment.source,
              shortPlayId: payment.shortPlayId,
              episode: payment.episode,
              skuType: payment.skuType,
              // 字符串格式 的 true 和 false
              isRestore: isRestore.value.toString())
          : await ApiStore.applePosting(
              receiptData: receiptData,
              skuId: skuId,
              skuProductId: payment.skuProductId,
              payAmount: productDetails.rawPrice.toString(),
              currency: currencyCode.value,
              shortPlayId: payment.shortPlayId,
              episode: payment.episode,
              skuModelConfigId: payment.skuModelConfigId,
              isRetain: payment.isRetain,
              skuType: payment.skuType,
            );

      // 使用与Android相同的验证逻辑
      return result == true;
    } catch (e) {
      FFLog.error("验证 Apple 支付错误: $e");
      return false;
    } finally {
      FFLog.info(
          "订阅接口调用完毕 finally isAutoRestore.value ${isAutoRestore.value}  isRestoreRunning $isRestoreRunning  是否关闭load (${(!isAutoRestore.value || !isRestoreRunning)})");
      if (!isAutoRestore.value) {
        Get.dismiss();
      }
    }
  }

  // 提前获取订阅列表 货币本地化
  Future<void> getSubscriptionProductList() async {
    var result = await ApiStore.getSubscriptionProductList();
    if (result != null) {
      result.productList?.forEach((item) {
        if (item.skuId.isNotEmpty) {
          subProductIds.add(item.skuId);

          if (item.inSubscription == true) {
            subProductEd.value = item;
          }
        }
      });

      subProductResult.value = buildLocaleSubList(result);
    }
  }

  // 提前获取商店列表 货币本地化
  Future<void> getProductList() async {
    var result = await ApiStore.getProductList();

    if (result != null) {
      storeProductResult.value = result;
      // 服务器没有返回货币符号
      try {
        currency.value =
            storeProductResult.value.skuInfoResponses?.first.currency ?? "";
        FFLog.debug("默认货币符号设置成功  ${currency.value}");
      } catch (e) {
        FFLog.debug("默认货币符号设置失败  $e");
      }

      storeProductResult.value.skuInfoResponses?.forEach((element) {
        if (Platform.isAndroid) {
          if (element.gpSkuId.isNotEmpty) {
            productIds.add(element.gpSkuId);
          }
        } else {
          if (element.iosSkuId.isNotEmpty) {
            productIds.add(element.iosSkuId);
          }
        }
      });

      if (storeProductResult.value.retainSkuInfoResponses != null) {
        var element = storeProductResult.value.retainSkuInfoResponses!;
        if (Platform.isAndroid) {
          if (element.gpSkuId.isNotEmpty) {
            productIds.add(element.gpSkuId);
          }
        } else {
          if (element.iosSkuId.isNotEmpty) {
            productIds.add(element.iosSkuId);
          }
        }
      }

      storeProductResult.value.subscribeSkuResponses?.forEach((element) {
        subProductIds.add(element.skuId.toString());
      });

      storeProductResult.value = buildLocaleStore(storeProductResult.value);
      FFLog.error("所有product Id  $productIds   $subProductIds");
    } else {
      FFLog.error("服务器返回商品为空  $result");
    }
  }

  Future<void> getUnlockProductList(
    int businessId,
    String scene,
    int dramaId,
  ) async {
    var result = await ApiStore.getUnlockProductList(
      businessId: businessId,
      scene: scene,
      dramaId: dramaId,
    );
    if (result != null) {
      unLockStoreProductResult.value = buildLocaleUnLockStore(result);
    }
  }

  @override
  void onClose() {
    try {
      if (Platform.isIOS) {
        final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
            _inAppPurchase
                .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
        iosPlatformAddition.setDelegate(null);
      }
      _subscription.cancel();
      _products = null;
      _paymentCallback = null;
    } catch (e) {
      FFLog.error("Error during payment service cleanup: $e");
    }
  }

  Future<void> refreshUserBalance() async {
    var result = await ApiStore.getUserBalance();
    if (result != null) {
      userBalance.value = result;
      Get.find<UserService>().updateBonus(result.bonus);
      Get.find<UserService>().updateCoins(result.coins);
      FFLog.debug("金币余额 ${userBalance.value.toJson()}");
    }
  }

  // 获取本地化货币价格
  String getIntroductoryPrice(ProductDetails productDetails) {
    FFLog.debug(
        "开始获取本地化首购价格  ${productDetails.id}  原本价格 ${productDetails.rawPrice}");
    if (Platform.isAndroid) {
      var subscriptionIdentifier = productDetails.rawPrice.toString();

      if (productDetails is GooglePlayProductDetails) {
        ProductDetailsWrapper skuDetails = productDetails.productDetails;

        // 检查是否有订阅优惠
        if (skuDetails.subscriptionOfferDetails?.isNotEmpty ?? false) {
          // 获取第一个优惠阶段的价格（通常是首订优惠）
          final offer = skuDetails.subscriptionOfferDetails!.first;
          FFLog.debug(
              "获取到优惠数据  ${skuDetails.subscriptionOfferDetails?.length}");
          if (offer.pricingPhases.isNotEmpty) {
            final firstPhase = offer.pricingPhases.first;
            // 转换为实际价格（micros 转换为标准单位）
            final priceAmount = firstPhase.priceAmountMicros / 1000000;
            FFLog.debug("获取到优惠数据  $priceAmount");
            subscriptionIdentifier = priceAmount.toString();
          }
        } else {
          FFLog.debug("未获取到优惠数据");
        }
      }

      return subscriptionIdentifier;
    } else {
      if (productDetails is AppStoreProductDetails) {
        SKProductWrapper skProduct = (productDetails).skProduct;
        return skProduct.introductoryPrice?.price ??
            productDetails.rawPrice.toString();
      } else {
        return productDetails.rawPrice.toString();
      }
    }
  }

  /// 完成对应的 Completer
  void _completePurchaseCompleter(String productId, bool result) {
    final completer = _purchaseCompletes[productId]?.completer;
    if (completer != null && !completer.isCompleted) {
      completer.complete(result);
      _purchaseCompletes.remove(productId);
    }
  }

  /// 清除未支付订单
  Future<void> _cancelAllWaitingPurchases() async {
    Get.toast(AppTrans.adWaitingTry());
    try {
      final paymentWrapper = SKPaymentQueueWrapper();
      final transactions = await paymentWrapper.transactions();
      for (var transaction in transactions) {
        await paymentWrapper.finishTransaction(transaction);
      }
    } catch (e) {
      Get.log("cancel all waiting purchases failed");
    }
  }

  Future<void> completeStorePurchase(PurchaseDetails purchase) async {
    await _inAppPurchase.completePurchase(purchase);
  }


  //本地化商店商品  服务器获取的商品列表 循环 google商店的列表 找到对应商品 把google商品的信息 修改到 服务器获取的商品列表里面
  StoreProductResult buildLocaleStore(StoreProductResult store) {
    FFLog.debug("编译本地化商店价格");

    var localStore = store;
    if (_products == null || _products!.isEmpty) {
      FFLog.error("No products available for purchase");
      // 未请求到google商店商品 返回服务器商品
      return store;
    }
    // 遍历 skuInfoResponses
    if (localStore.skuInfoResponses != null && _products != null) {
      for (var sku in localStore.skuInfoResponses!) {
        final productDetails =
            _getProduct(_products, Platform.isIOS ? sku.iosSkuId : sku.gpSkuId);
        if (productDetails != null) {
          sku.recharge = productDetails.rawPrice.toString();
        }
      }
    }

    // 遍历 retainSkuInfoResponses
    if (localStore.retainSkuInfoResponses != null && _products != null) {
      final sku = localStore.retainSkuInfoResponses!;
      final productDetails =
          _getProduct(_products, Platform.isIOS ? sku.iosSkuId : sku.gpSkuId);
      if (productDetails != null) {
        sku.recharge = productDetails.rawPrice.toString();
      }
    }

    // 遍历 subscribeSkuResponses
    if (localStore.subscribeSkuResponses != null && _products != null) {
      for (var sku in localStore.subscribeSkuResponses!) {
        final productDetails = _getProduct(_products, sku.skuId);
        if (productDetails != null) {
          sku.payAmount = productDetails.rawPrice.toString();
          sku.firstAmount = getIntroductoryPrice(productDetails);
          FFLog.error("订阅商品 本地化  ${sku.payAmount}  首购价格 ${sku.firstAmount}");
        }
      }
    }

    return localStore;
  }

  // 本地化金额 解锁列表
  UnLockStoreProductResult buildLocaleUnLockStore(
      UnLockStoreProductResult store) {
    FFLog.debug("解锁页编译本地化价格");
    var localStore = store;
    if (_products == null || _products!.isEmpty) {
      FFLog.error("No products available for purchase");
      // 未请求到google商店商品 返回服务器商品
      return store;
    }
    // 遍历 skuInfoResponses
    if (localStore.skuInfoResponses != null && _products != null) {
      for (var sku in localStore.skuInfoResponses!) {
        final productDetails =
            _getProduct(_products, Platform.isIOS ? sku.iosSkuId : sku.gpSkuId);
        if (productDetails != null) {
          sku.recharge = productDetails.rawPrice.toString();
          FFLog.error("解锁页 订阅商品 本地化 ${currency.value}");
        }
      }
    }

    // 遍历 retainSkuInfoResponses
    if (localStore.retainSkuInfoResponses != null && _products != null) {
      final sku = localStore.retainSkuInfoResponses!;
      final productDetails =
          _getProduct(_products, Platform.isIOS ? sku.iosSkuId : sku.gpSkuId);
      if (productDetails != null) {
        sku.recharge = productDetails.rawPrice.toString();
        FFLog.error("解锁页 订阅商品 本地化 ");
      }
    }

    // 遍历 subscribeSkuResponses
    if (localStore.subscribeSkuResponses != null && _products != null) {
      for (var sku in localStore.subscribeSkuResponses!) {
        final productDetails = _getProduct(_products, sku.skuId);
        if (productDetails != null) {
          sku.payAmount = productDetails.rawPrice.toString();
          sku.firstAmount = getIntroductoryPrice(productDetails);
          FFLog.error(
              "解锁页 订阅商品 本地化  ${sku.payAmount}  首购价格 ${sku.firstAmount}");
        }
      }
    }

    return localStore;
  }

  // 本地化金额 订阅商品列表
  StoreSubscriptionProductResult buildLocaleSubList(
      StoreSubscriptionProductResult subProductList) {
    if (subProductList.productList?.isEmpty == true || _products == null) {
      FFLog.error("未获取到商店商品 无法本地化");
      return subProductList;
    }
    for (var subProduct in subProductList.productList!) {
      final productDetails =
          _getProduct(_products, subProduct.skuId.toString());
      if (productDetails != null) {
        subProduct.payAmount =
            productDetails.price.replaceAll(productDetails.currencySymbol, '');
        subProduct.firstAmount = getIntroductoryPrice(productDetails);
        FFLog.error(
            "订阅商品 本地化  ${subProduct.payAmount}  首购价格 ${subProduct.firstAmount}");
      } else {
        FFLog.error("订阅商品 无法本地化  ${subProduct.skuId.toString()} ");
      }
    }
    return subProductList;
  }

  /// 2. 原用户进入充值页会触发自动补单机制，
  /// 新增：订阅页/我的页面/解锁页展示这几个场景，即在用户进入这四个场景时，重试发货，直至成功(服务端判定订单合法后才发货)
  Future<void> startRecover({String from = EventValue.topUp}) async {
    if (!Config.isProduction) {
      return;
    }
    currStrScene.value = from;
    try {
      if (!await _inAppPurchase.isAvailable()) {
        FFLog.error("无法连接至商店");
        return;
      }

      //添加自动补单 订阅补单
      restorePurchases(isAuto: true);

      if (isRestoreRunning) {
        FFLog.info("内购补单流程执行中... 不允许发起内购补单");
        return;
      }
      FFLog.info("开始内购补单流程 ... ");
      var restoreList = ProductStorage.getRestorePaymentList();
      if (restoreList == null || restoreList.isEmpty == true) {
        FFLog.info("没有需要内购补单的订单  $restoreList");
        PaymentEvent.submitRestoreResult(
            type: EventValue.auto,
            result: "0",
            genre: EventValue.inapp,
            strScene: from);
        return;
      } else {
        FFLog.info("需要补单的内购skuId 为空不进行补单  $restoreList");
        restoreList.forEach((skuId) async {
          var data = ProductStorage.getPaymentBySkuId(skuId);

          FFLog.info("需要内购补单的数据  $data");
          if (data != null) {
            // 根据原始数据恢复订单对象
            var purchase = data.rawData.toPurchaseDetails();
            if (Platform.isIOS) {
              var result = await ApiStore.appleRecover(
                  receiptData: data.receiptData, skuId: data.skuId);

              if (result != null) {
                // 补单完成上报商店消耗
                completeStorePurchase(purchase);

                ProductStorage.removePayment(skuId);
                PaymentEvent.submitRestoreResult(
                    type: EventValue.auto,
                    genre: EventValue.inapp,
                    strScene: from,
                    coins: result.coinsRecover.toString(),
                    bonus: result.bonusRecover.toString());
              }

              FFLog.info("ios内购 补单结果   ${result?.toJson()}  ${data.rawData}");
            } else {
              var result = await ApiStore.googleRecover(
                  skuId: data.skuId,
                  purchaseData: data.purchaseData,
                  signature: data.signature,
                  skuType: data.skuType,
                  skuProductId: data.skuProductId);
              FFLog.info(
                  "android内购 补单结果   ${result?.toJson()} ${data.rawData}");
              result?.payRecoverAndroidInfoResponses?.forEach((item) {
                // 补单完成上报商店消耗
                completeStorePurchase(purchase);
                ProductStorage.removePayment(skuId);
                PaymentEvent.submitRestoreResult(
                    type: EventValue.auto,
                    genre: EventValue.inapp,
                    strScene: from,
                    coins: item.coinsRecover.toString(),
                    bonus: item.bonusRecover.toString());
              });
            }
            refreshUserBalance();
          } else {
            FFLog.error("内购恢复购买失败。补单数据为空 ");
          }
        });
      }
    } catch (e) {
      FFLog.error("补单失败 ${e.toString()}");
    }
  }

  _handlePurchaseError(PurchaseDetails purchase) {
    var isSubscriptionProduct = _isSubscriptionProduct(purchase.productID);
    final Payment? payment = _purchaseCompletes[purchase.productID];
    if (payment != null) {
      final ProductDetails? productDetails =
          _getProduct(_products, payment.skuId);

      if (productDetails != null) {
        // todo 提交创建失败埋点
        PaymentEvent.submitOrderCreateFail(payment, currencyCode.value, purchase.error?.code ?? "",
            isSubscriptionProduct: isSubscriptionProduct);
      }
    }
  }

  ProductDetails? getProduct(skuId) {
    return _getProduct(_products, skuId);
  }
}
